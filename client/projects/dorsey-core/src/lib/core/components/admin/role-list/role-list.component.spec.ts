import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError, Subject } from 'rxjs';
import { GridApi } from 'ag-grid-community';

import { RoleListComponent } from './role-list.component';
import { RoleService } from '../../../services/admin/role.service';
import { ToastService } from '../../../services/toast.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { IRole } from '../../../models/admin/roles/role.model';
import { RoleActions } from '../../../models/enums/role-actions';
import { FormAction } from '../../../models/form-action';
import { DataGridMessage } from '../../datagrid/models/datagrid-message.model';
import { DatagridActionsCta } from '../../datagrid/models/enums/datagrid-actions-cta';

describe('RoleListComponent', () => {
  let component: RoleListComponent;
  let fixture: ComponentFixture<RoleListComponent>;
  let mockRoleService: jest.Mocked<RoleService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: jest.Mocked<ActivatedRoute>;

  const mockRoles: IRole[] = [
    {
      roleId: '1',
      name: 'Admin',
      department: 'IT',
      description: 'Administrator role',
      superior: null,
      superiorId: null,
      capabilities: []
    },
    {
      roleId: '2',
      name: 'Manager',
      department: 'Sales',
      description: 'Sales manager role',
      superior: { roleId: '1', name: 'Admin' },
      superiorId: '1',
      capabilities: []
    },
    {
      roleId: '3',
      name: 'Employee',
      department: 'Sales',
      description: 'Sales employee role',
      superior: { roleId: '2', name: 'Manager' },
      superiorId: '2',
      capabilities: []
    }
  ];

  beforeEach(async () => {
    const mockEditingStateSubject = new Subject<FormAction>();

    mockRoleService = {
      findAll: jest.fn().mockReturnValue(of(mockRoles)),
      updateRole: jest.fn().mockReturnValue(of({}))
    } as any;

    mockToastService = {
      displaySuccess: jest.fn(),
      displayError: jest.fn()
    } as any;

    mockEditingStateService = {
      getValue: jest.fn().mockReturnValue(mockEditingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: {
        isEditing: false,
        hasChanges: false
      }
    } as any;

    mockDialogMessageService = {
      displayError: jest.fn()
    } as any;

    mockRouter = {
      navigate: jest.fn().mockResolvedValue(true),
      navigateByUrl: jest.fn().mockResolvedValue(true)
    } as any;

    mockActivatedRoute = {
      snapshot: { params: {}, queryParams: {} },
      params: of({}),
      queryParams: of({})
    } as any;

    await TestBed.configureTestingModule({
      declarations: [RoleListComponent],
      providers: [
        { provide: RoleService, useValue: mockRoleService },
        { provide: ToastService, useValue: mockToastService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(RoleListComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.title).toBe('Role List');
      expect(component.roleAction).toBe(RoleActions);
      expect(component.colors.length).toBe(8);
      expect(component.departmentColorMap).toBeInstanceOf(Map);
      expect(component.nodes).toEqual([]);
      expect(component.links).toEqual([]);
      expect(component.errors).toEqual([]);
      expect(component.defCapabilities).toEqual([]);
      expect(component.WAIT_GRAPH_MS).toBe(1000);
      expect(component.canAdjust).toBe(true);
    });

    it('should have correct layout settings', () => {
      expect(component.layoutSettings.orientation).toBe('TB');
      expect(component.curve).toBeDefined();
      expect(component.layout).toBeDefined();
    });
  });
